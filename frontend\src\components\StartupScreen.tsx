import React, { useState, useEffect, useRef } from 'react';
import { getMindBooksList, loadMindBook, getSessionInfo, restoreAutoSavedSession } from '../core/services/MindBookPersistenceService';
import type { MindBookListItem } from '../core/services/MindBookPersistenceService';
import { useMindBookStore } from '../core/state/MindBookStore';
import { useChatStore } from '../governance/chat/state/ChatStore';
import { LogoAssets } from '../utils/assetPaths';
import './StartupScreen.css';

// Use the asset path utility for logo paths
const logoConfig = LogoAssets.getPrimaryLogo();

interface StartupScreenProps {
  onNavigateToMindBook: () => void;
  onNewMindBook: () => void;
  onBrowseAll: () => void;
}

const StartupScreen: React.FC<StartupScreenProps> = ({
  onNavigateToMindBook,
  onNewMindBook,
  onBrowseAll
}) => {
  const [recentMindBooks, setRecentMindBooks] = useState<MindBookListItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const navigateRef = useRef(onNavigateToMindBook);
  const [userRequestedStartup] = useState(() => {
    // Check if this was a user-requested startup
    const params = new URLSearchParams(window.location.search);
    return params.get('userRequested') === 'true';
  });

  // Update ref when prop changes
  useEffect(() => {
    navigateRef.current = onNavigateToMindBook;
  }, [onNavigateToMindBook]);

  useEffect(() => {
    const initializeApp = async () => {
      try {
        setIsLoading(true);
        setError(null);

        // TEMPORARILY DISABLED: Auto-restoration to allow seeing the start page with saved sessions
        // Skip auto-restoration when user explicitly requested startup
        if (false && !userRequestedStartup) { // DISABLED: Set to false to always show start page
          const sessionInfo = getSessionInfo();
          if (sessionInfo.hasAutoSave) {
            console.log('StartupScreen: Found auto-saved session, but user did not explicitly request startup');
            const restored = restoreAutoSavedSession();
            if (restored) {
              console.log('StartupScreen: Auto-saved session restored, navigating to MindBook');
              navigateRef.current();
              return;
            }
          }
        } else {
          console.log('StartupScreen: Auto-restoration disabled - showing start page with saved sessions');
        }

        // Load recent MindBooks
        await loadRecentMindBooks();
        setIsLoading(false);
      } catch (err: any) {
        console.error('Startup error:', err);
        setError(err?.message || 'Failed to initialize application');
        setIsLoading(false);
      }
    };

    initializeApp();
  }, []); // No dependencies needed

  const loadRecentMindBooks = async () => {
    try {
      console.log('🚀 StartupScreen: Loading recent MindBooks...');

      // Get the list of MindBooks
      const allMindBooks = getMindBooksList();
      console.log('🚀 StartupScreen: Got MindBooks list:', allMindBooks);

      // Check current MindBook store state
      const currentStore = useMindBookStore.getState();
      console.log('🚀 StartupScreen: Current store state:', {
        sheets: currentStore.sheets.length,
        name: currentStore.name
      });

      // Create a list that includes current session if it has sheets OR if there's an auto-saved session
      let recentList = [...allMindBooks];

      // Check for auto-saved session even if current store is empty
      const sessionInfo = getSessionInfo();
      const hasCurrentSession = currentStore.sheets.length > 0 || sessionInfo.hasAutoSave;

      if (hasCurrentSession) {
        const sheetsCount = currentStore.sheets.length || (sessionInfo.hasAutoSave ? 'auto-saved' : 0);
        const currentSession = {
          id: 'current_session',
          name: 'Current Session',
          description: typeof sheetsCount === 'number' ? `${sheetsCount} sheets` : sheetsCount,
          savedAt: sessionInfo.lastSaved || Date.now(),
          sheetsCount: typeof sheetsCount === 'number' ? sheetsCount : 1
        };
        recentList.unshift(currentSession);
      }

      // Sort by savedAt and take first 5
      const recent = recentList
        .sort((a, b) => b.savedAt - a.savedAt)
        .slice(0, 5);

      setRecentMindBooks(recent);
    } catch (error: any) {
      console.error('Failed to load recent MindBooks:', error);
      setRecentMindBooks([]);
      throw new Error(error?.message || 'Failed to load recent MindBooks');
    }
  };

  const handleLoadMindBook = (mindBookId: string) => {
    try {
      if (mindBookId === 'current_session') {
        navigateRef.current();
        return;
      }
      
      const success = loadMindBook(mindBookId);
      if (success) {
        navigateRef.current();
      } else {
        setError('Failed to load MindBook');
      }
    } catch (error: any) {
      console.error('Failed to load MindBook:', error);
      setError(error?.message || 'Failed to load MindBook');
    }
  };

  const handleNewMindBook = () => {
    try {
      // Clear any existing session data
      const mindBookStore = useMindBookStore.getState();
      mindBookStore.setSheets([]);
      mindBookStore.setName(null);
      
      // Clear chat messages
      const chatStore = useChatStore.getState();
      chatStore.clearMessages();
      
      // Clear only auto-save and session data, preserve saved MindBooks
      const keysToRemove = Object.keys(localStorage).filter(k => 
        k.includes('mindbook_autosave') || 
        k.includes('active_mindbook') ||
        (k.startsWith('mindmap_sheet_') && !k.includes('_saved_'))
      );
      keysToRemove.forEach(key => localStorage.removeItem(key));
      
      navigateRef.current();
    } catch (error: any) {
      console.error('Error creating new MindBook:', error);
      setError(error?.message || 'Failed to create new MindBook');
    }
  };

  const formatDate = (savedAt: number) => {
    return new Date(savedAt).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const generateFilePath = (mindBookName: string, savedAt: number) => {
    // Create a simulated file path for the MindBook
    const sanitizedName = mindBookName.replace(/[^a-zA-Z0-9\s-_]/g, '').replace(/\s+/g, '_');
    const date = new Date(savedAt);
    const dateFolder = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
    return `~/Documents/MindBack/${dateFolder}/${sanitizedName}.mbmb`;
  };

  if (isLoading) {
    return (
      <div className="startup-screen-clean">
        <div className="startup-content-wrapper">
          <div className="startup-logo-section">
            <img 
              src={logoConfig.src} 
              alt="MindBack Logo" 
              className="startup-logo-centered"
              onError={logoConfig.onError}
            />
          </div>
          <div className="startup-loading">
            <p>Loading...</p>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="startup-screen-clean">
        <div className="startup-content-wrapper">
          <div className="startup-logo-section">
            <img 
              src={logoConfig.src} 
              alt="MindBack Logo" 
              className="startup-logo-centered"
              onError={logoConfig.onError}
            />
          </div>
          <div className="startup-error">
            <h3>Error</h3>
            <p>{error}</p>
            <button onClick={() => window.location.reload()}>Retry</button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="startup-screen-clean">
      <div className="startup-content-wrapper">
        <div className="startup-logo-section">
          <img 
            src={logoConfig.src} 
            alt="MindBack Logo" 
            className="startup-logo-centered"
            onError={logoConfig.onError}
          />
        </div>

        <div className="startup-actions-clean">
          <button 
            className="startup-button-left"
            onClick={onBrowseAll}
          >
            Open MindBook
          </button>
          <button 
            className="startup-button-right"
            onClick={handleNewMindBook}
          >
            Create New MindBook
          </button>
        </div>

        <div className="startup-recent-section">
          {recentMindBooks.length > 0 ? (
            <div className="startup-recent-list">
              {recentMindBooks.map((mindBook) => (
                <div 
                  key={mindBook.id} 
                  className="startup-recent-item"
                  onClick={() => handleLoadMindBook(mindBook.id)}
                >
                  <span className="startup-recent-name">{mindBook.name}</span>
                  <span className="startup-recent-path">
                    {mindBook.id === 'current_session' 
                      ? '~/Documents/MindBack/temp/current_session.mbmb'
                      : generateFilePath(mindBook.name, mindBook.savedAt)
                    }
                  </span>
                </div>
              ))}
            </div>
          ) : (
            <div className="startup-no-mindbooks">
              there are no previous mindbooks
            </div>
          )}
        </div>
      </div>

      <div className="startup-shortcuts-hint">
        Press <kbd>Esc</kbd> for options • <kbd>Ctrl+K</kbd> to clear session
      </div>
    </div>
  );
};

export default StartupScreen; 
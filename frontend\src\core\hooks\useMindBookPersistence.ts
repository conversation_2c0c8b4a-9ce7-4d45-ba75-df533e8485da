/**
 * useMindBookPersistence.ts
 * 
 * React hook that provides MindBook persistence functionality using context-based store access.
 * This hook solves the store fragmentation issue by ensuring all persistence operations
 * use the same store instances as the UI components.
 */

import { useCallback } from 'react';
import { useMindBookStoreContext, useChatStoreContext } from '../context/StoreContexts';
import { useContextStore } from '../../features/context/store/ContextStore';
import { pureMindBookPersistenceService } from '../services/MindBookPersistenceService';

/**
 * Hook that provides MindBook persistence operations using context-based store access
 */
export function useMindBookPersistence() {
  // Get store instances from contexts - this ensures we use the same instances as UI components
  const mindBookStore = useMindBookStoreContext();
  const chatStore = useChatStoreContext();
  const contextStore = useContextStore();

  /**
   * Save a MindBook using context-based store access
   */
  const saveMindBook = useCallback((name: string, description?: string): boolean => {
    return pureMindBookPersistenceService.saveMindBookWithStores(
      name,
      description,
      mindBookStore,
      chatStore,
      contextStore
    );
  }, [mindBookStore, chatStore, contextStore]);

  /**
   * Load a MindBook using context-based store access
   */
  const loadMindBook = useCallback((mindBookId: string): boolean => {
    return pureMindBookPersistenceService.loadMindBookWithStores(
      mindBookId,
      mindBookStore,
      chatStore,
      contextStore
    );
  }, [mindBookStore, chatStore, contextStore]);

  /**
   * Auto-save session using context-based store access
   */
  const autoSaveSession = useCallback((): boolean => {
    return pureMindBookPersistenceService.autoSaveSessionWithStores(
      mindBookStore,
      chatStore,
      contextStore
    );
  }, [mindBookStore, chatStore, contextStore]);

  /**
   * Get list of saved MindBooks
   */
  const getMindBooksList = useCallback(() => {
    return pureMindBookPersistenceService.getMindBooksList();
  }, []);

  return {
    saveMindBook,
    loadMindBook,
    autoSaveSession,
    getMindBooksList,
    // Expose store instances for advanced use cases
    stores: {
      mindBookStore,
      chatStore,
      contextStore
    }
  };
}

/**
 * Hook for non-React contexts that need to perform persistence operations
 * This should only be used in special cases where React hooks cannot be used
 */
export function createPersistenceOperations() {
  return {
    /**
     * Save MindBook with explicit store instances
     * Use this only when you have store instances from outside React context
     */
    saveMindBookWithStores: pureMindBookPersistenceService.saveMindBookWithStores.bind(pureMindBookPersistenceService),
    
    /**
     * Load MindBook with explicit store instances
     * Use this only when you have store instances from outside React context
     */
    loadMindBookWithStores: pureMindBookPersistenceService.loadMindBookWithStores.bind(pureMindBookPersistenceService),
    
    /**
     * Auto-save with explicit store instances
     * Use this only when you have store instances from outside React context
     */
    autoSaveSessionWithStores: pureMindBookPersistenceService.autoSaveSessionWithStores.bind(pureMindBookPersistenceService),
    
    /**
     * Get MindBooks list (doesn't require store access)
     */
    getMindBooksList: pureMindBookPersistenceService.getMindBooksList.bind(pureMindBookPersistenceService)
  };
}

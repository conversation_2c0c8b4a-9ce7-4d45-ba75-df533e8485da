/**
 * Pure Store Registry - No React Dependencies
 * 
 * This module provides a pure JavaScript store registry that doesn't use any React hooks.
 * It serves as the foundation for the React context-based store access pattern.
 */

import { createMindMapStore } from '../state/MindMapStore';
import type { MindMapStore } from '../state/MindMapStore';

/**
 * Pure store registry class that manages store instances without React hooks
 */
class PureStoreRegistry {
  private stores = new Map<string, MindMapStore>();
  private activeSheetId: string | null = null;

  /**
   * Get or create a store for a sheet ID
   * This is a pure function - no React hooks
   */
  getStore(sheetId: string): MindMapStore {
    if (!this.stores.has(sheetId)) {
      console.log('PureStoreRegistry: Creating new store for sheet:', sheetId);
      const store = createMindMapStore();
      this.stores.set(sheetId, store);
    }
    return this.stores.get(sheetId)!;
  }

  /**
   * Check if a store exists for a sheet ID
   */
  hasStore(sheetId: string): boolean {
    return this.stores.has(sheetId);
  }

  /**
   * Remove a store for a sheet ID
   */
  removeStore(sheetId: string): void {
    this.stores.delete(sheetId);
    if (this.activeSheetId === sheetId) {
      this.activeSheetId = null;
    }
  }

  /**
   * Set the active sheet ID
   */
  setActiveSheetId(sheetId: string): void {
    this.activeSheetId = sheetId;
  }

  /**
   * Get the active sheet ID
   */
  getActiveSheetId(): string | null {
    return this.activeSheetId;
  }

  /**
   * Get all store IDs
   */
  getAllStoreIds(): string[] {
    return Array.from(this.stores.keys());
  }

  /**
   * Clear all stores
   */
  clearAll(): void {
    this.stores.clear();
    this.activeSheetId = null;
  }
}

/**
 * Global pure store registry instance
 * This is a singleton that can be safely accessed from anywhere
 */
export const pureStoreRegistry = new PureStoreRegistry();

/**
 * Pure helper functions that don't use React hooks
 * These can be safely called from anywhere in the application
 */

export function getPureMindMapStore(sheetId: string): MindMapStore {
  return pureStoreRegistry.getStore(sheetId);
}

export function hasPureMindMapStore(sheetId: string): boolean {
  return pureStoreRegistry.hasStore(sheetId);
}

export function removePureMindMapStore(sheetId: string): void {
  return pureStoreRegistry.removeStore(sheetId);
}

export function setPureActiveSheetId(sheetId: string): void {
  return pureStoreRegistry.setActiveSheetId(sheetId);
}

export function getPureActiveSheetId(): string | null {
  return pureStoreRegistry.getActiveSheetId();
}

/**
 * Save store state for a sheet
 * This is a pure function that doesn't use React hooks
 */
export function savePureMindMapStoreState(sheetId: string): void {
  const store = pureStoreRegistry.getStore(sheetId);
  // Store state is automatically persisted by Zustand
  console.log('PureStoreRegistry: State saved for sheet:', sheetId);
}

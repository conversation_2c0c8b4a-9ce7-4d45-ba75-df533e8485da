/**
 * MindBookPersistenceService.ts
 * 
 * Unified persistence service for MindBooks - the complete solution for session management.
 * This service replaces all fragmented session persistence approaches with a single,
 * comprehensive system that saves and restores complete MindBooks.
 * 
 * A MindBook is like an Excel workbook - it contains multiple sheets (MindSheets)
 * of different types (ChatFork, MindMap, etc.) and maintains session state.
 * 
 * Context Settings Integration:
 * - MindBooks can reference Context Settings by ID
 * - When loading a MindBook, associated Context Settings are automatically loaded
 * - Context Settings are managed separately and can be reused across MindBooks
 */

import { useMindBookStore } from '../state/MindBookStore';
import { SheetData, MindSheetContentType } from '../state/StoreTypes';
import { saveSheetMindMapStoreState, loadSheetMindMapStoreState, getActiveSheetIds, removeSheetMindMapStore } from './StoreService';
import { useContextStore } from '../../features/context/store/ContextStore';
import { useChatStore } from '../../governance/chat/state/ChatStore';
import RegistrationManager, { EventType } from './RegistrationManager';

// Storage keys
const MINDBOOK_STORAGE_PREFIX = 'mindbook_';
const MINDBOOKS_LIST_KEY = 'mindbooks_list';
const ACTIVE_MINDBOOK_KEY = 'active_mindbook';
const AUTO_SAVE_KEY = 'mindbook_autosave';

interface MindBookData {
  id: string;
  name: string;
  sheets: SheetData[];
  activeSheetId: string | null;
  contextSettingsId?: string; // Reference to Context Settings
  chatMessages?: any[]; // CRITICAL: Chat message persistence for memory continuity
  savedAt: number;
  version: string;
  metadata?: {
    description?: string;
    tags?: string[];
    creator?: string;
  };
}

interface MindBookListItem {
  id: string;
  name: string;
  savedAt: number;
  sheetsCount: number;
  description?: string;
}

/**
 * MindBookPersistenceService - Unified persistence for complete MindBook sessions
 */
class MindBookPersistenceService {
  private static instance: MindBookPersistenceService;
  private readonly version = '2.2.0'; // Updated version for chat message persistence and memory integration
  private autoSaveTimeout: NodeJS.Timeout | null = null;

  private constructor() {
    console.log('MindBookPersistenceService: Initialized v' + this.version);
  }

  public static getInstance(): MindBookPersistenceService {
    if (!MindBookPersistenceService.instance) {
      MindBookPersistenceService.instance = new MindBookPersistenceService();
    }
    return MindBookPersistenceService.instance;
  }

  /**
   * Clear all session data
   */
  public clearSession(): void {
    try {
      // Get current session info before clearing for logging
      const store = useMindBookStore.getState();
      const currentSheets = store.sheets.length;
      const currentName = store.name;
      
      // Register session close event if there was an active session
      if (currentSheets > 0) {
        RegistrationManager.registerEvent(EventType.MINDBOOK_CLOSED, {
          name: currentName || 'Unnamed Session',
          sheets: currentSheets,
          timestamp: new Date().toISOString()
        });
      }

      // Clear MindBook store
      store.setSheets([]);
      store.setActiveSheet(null);

      // Clear chat store
      const chatStore = useChatStore.getState();
      chatStore.clearMessages();

      // Clear context store
      const contextStore = useContextStore.getState();
      contextStore.clearCurrentContextSettings();

      // Clear localStorage
      const keysToRemove = Object.keys(localStorage).filter(k => 
        k.includes('mindbook') || 
        k.includes('mindmap') || 
        k === AUTO_SAVE_KEY
      );
      
      console.log('MindBookPersistenceService: Clearing session data...', keysToRemove);
      
      keysToRemove.forEach(key => {
        try {
          localStorage.removeItem(key);
        } catch (error) {
          console.error(`MindBookPersistenceService: Failed to remove key ${key}:`, error);
          // Continue with other keys even if one fails
        }
      });

      console.log('MindBookPersistenceService: Session cleared successfully');
    } catch (error) {
      console.error('MindBookPersistenceService: Failed to clear session:', error);
      throw new Error('Failed to clear session: ' + error.message);
    }
  }

  /**
   * Save the current session as an auto-save (temporary)
   */
  public autoSaveSession(): boolean {
    try {
      const store = useMindBookStore.getState();
      
      if (store.sheets.length === 0) {
        console.log('MindBookPersistenceService: No sheets to auto-save');
        return true;
      }

      // Get current context settings ID
      const contextStore = useContextStore.getState();
      const currentContextSettingsId = contextStore.currentContextSettings?.id || null;

      // CRITICAL: Get chat messages for memory persistence
      const chatStore = useChatStore.getState();
      const allMessages = chatStore.messages;
      const chatMessages = allMessages.filter(msg => msg.id !== 'welcome');

      // Debug: Log what we're saving
      console.log('MindBookPersistenceService: DEBUG - Auto-save chat data:', {
        totalMessages: allMessages.length,
        filteredMessages: chatMessages.length,
        messages: chatMessages.map(msg => ({ id: msg.id, text: msg.text.substring(0, 50) + '...', sender: msg.sender }))
      });

      // Log context settings capture for debugging
      if (currentContextSettingsId) {
        console.log('MindBookPersistenceService: Auto-saving with context settings:', currentContextSettingsId);
      } else {
        console.log('MindBookPersistenceService: Auto-saving without context settings');
      }

      console.log('MindBookPersistenceService: Auto-saving with', chatMessages.length, 'chat messages');

      const autoSaveData: MindBookData = {
        id: 'autosave',
        name: 'Auto-saved Session',
        sheets: store.sheets,
        activeSheetId: store.activeSheetId,
        contextSettingsId: currentContextSettingsId,
        chatMessages: chatMessages.length > 0 ? chatMessages : undefined,
        savedAt: Date.now(),
        version: this.version
      };

      // Save individual sheet states first
      try {
        this.saveAllSheetStates(store.sheets);
      } catch (error) {
        console.error('MindBookPersistenceService: Failed to save sheet states:', error);
        // Continue with main save even if sheet states fail
      }

      // Serialize and save main data
      const serializedData = JSON.stringify(autoSaveData);
      localStorage.setItem(AUTO_SAVE_KEY, serializedData);

      // Verify the save
      const savedData = localStorage.getItem(AUTO_SAVE_KEY);
      if (!savedData) {
        throw new Error('Failed to verify auto-save data in localStorage');
      }

      console.log('MindBookPersistenceService: Auto-saved session with', store.sheets.length, 'sheets and context settings:', currentContextSettingsId);
      return true;
    } catch (error) {
      console.error('MindBookPersistenceService: Auto-save failed:', error);
      // Re-throw to ensure calling code knows about the failure
      throw new Error('Auto-save failed: ' + error.message);
    }
  }

  /**
   * Restore the auto-saved session (called on app startup)
   */
  public restoreAutoSavedSession(): boolean {
    try {
      const autoSaveData = localStorage.getItem(AUTO_SAVE_KEY);
      
      if (!autoSaveData) {
        console.log('MindBookPersistenceService: No auto-saved session found');
        return false;
      }

      const sessionData: MindBookData = JSON.parse(autoSaveData);
      console.log('MindBookPersistenceService: Restoring auto-saved session:', sessionData.name);
      
      const success = this.restoreMindBookData(sessionData);
      
      if (success) {
        // Register the session restoration event
        RegistrationManager.registerEvent(EventType.MINDBOOK_OPENED, {
          name: sessionData.name || 'Auto-saved Session',
          type: 'auto_restore',
          sheets: sessionData.sheets.length,
          timestamp: new Date().toISOString()
        });
      }
      
      return success;
    } catch (error) {
      console.error('MindBookPersistenceService: Failed to restore auto-saved session:', error);
      return false;
    }
  }

  /**
   * Save the current session as a named MindBook
   */
  public saveMindBook(name: string, description?: string): boolean {
    try {
      const store = useMindBookStore.getState();
      
      if (store.sheets.length === 0) {
        console.warn('MindBookPersistenceService: Cannot save empty MindBook');
        return false;
      }

      const mindBookId = this.generateMindBookId(name);
      
      // Get current context settings ID
      const contextStore = useContextStore.getState();
      const currentContextSettingsId = contextStore.currentContextSettings?.id || null;

      // CRITICAL: Get chat messages for memory persistence
      const chatStore = useChatStore.getState();
      const chatMessages = chatStore.messages.filter(msg => msg.id !== 'welcome');

      // Log context settings capture for debugging
      if (currentContextSettingsId) {
        console.log('MindBookPersistenceService: Saving MindBook with context settings:', currentContextSettingsId);
      } else {
        console.log('MindBookPersistenceService: Saving MindBook without context settings');
      }

      console.log('MindBookPersistenceService: Saving MindBook with', chatMessages.length, 'chat messages');

      const mindBookData: MindBookData = {
        id: mindBookId,
        name: name.trim(),
        sheets: store.sheets,
        activeSheetId: store.activeSheetId,
        contextSettingsId: currentContextSettingsId,
        chatMessages: chatMessages.length > 0 ? chatMessages : undefined,
        savedAt: Date.now(),
        version: this.version,
        metadata: {
          description: description?.trim(),
          creator: 'mindback.ai',
          tags: this.extractTagsFromSheets(store.sheets)
        }
      };

      // Save individual sheet states
      this.saveAllSheetStates(store.sheets);

      // Save the MindBook
      const storageKey = MINDBOOK_STORAGE_PREFIX + mindBookId;
      localStorage.setItem(storageKey, JSON.stringify(mindBookData));

      // Update MindBooks list
      this.updateMindBooksList(mindBookData);

      // Set as active MindBook
      localStorage.setItem(ACTIVE_MINDBOOK_KEY, mindBookId);

      // Update the store name to reflect the saved MindBook name
      store.setName(name.trim());

      // Register the save event
      RegistrationManager.registerEvent(EventType.MINDBOOK_SAVED, {
        name: name.trim(),
        id: mindBookId,
        sheets: mindBookData.sheets.length,
        description: description?.trim() || null,
        timestamp: new Date().toISOString()
      });

      console.log('MindBookPersistenceService: MindBook saved successfully:', name);
      return true;
    } catch (error) {
      console.error('MindBookPersistenceService: Failed to save MindBook:', error);
      return false;
    }
  }

  /**
   * Load a saved MindBook by ID
   */
  public loadMindBook(mindBookId: string): boolean {
    try {
      const storageKey = MINDBOOK_STORAGE_PREFIX + mindBookId;
      const savedData = localStorage.getItem(storageKey);
      
      if (!savedData) {
        console.error('MindBookPersistenceService: MindBook not found:', mindBookId);
        return false;
      }

      const mindBookData: MindBookData = JSON.parse(savedData);
      console.log('MindBookPersistenceService: Loading MindBook:', mindBookData.name);
      
      const success = this.restoreMindBookData(mindBookData);
      
      if (success) {
        // Set as active MindBook
        localStorage.setItem(ACTIVE_MINDBOOK_KEY, mindBookId);
        
        // Register the mindbook opening event with welcome back message
        RegistrationManager.registerEvent(EventType.MINDBOOK_OPENED, {
          name: mindBookData.name,
          id: mindBookId,
          type: 'user_load',
          sheets: mindBookData.sheets.length,
          lastSaved: new Date(mindBookData.savedAt).toLocaleString(),
          timestamp: new Date().toISOString()
        });
      }
      
      return success;
    } catch (error) {
      console.error('MindBookPersistenceService: Failed to load MindBook:', error);
      return false;
    }
  }

  /**
   * Delete a saved MindBook
   */
  public deleteMindBook(mindBookId: string): boolean {
    try {
      const storageKey = MINDBOOK_STORAGE_PREFIX + mindBookId;
      
      // Get the MindBook data before deleting to clean up sheet states
      const savedData = localStorage.getItem(storageKey);
      if (savedData) {
        const mindBookData: MindBookData = JSON.parse(savedData);
        
        // Clean up individual sheet states
        mindBookData.sheets.forEach(sheet => {
          localStorage.removeItem(`mindmap_sheet_${sheet.id}`);
        });
      }
      
      // Remove the MindBook
      localStorage.removeItem(storageKey);
      
      // Update MindBooks list
      const mindBooksList = this.getMindBooksList();
      const updatedList = mindBooksList.filter(item => item.id !== mindBookId);
      localStorage.setItem(MINDBOOKS_LIST_KEY, JSON.stringify(updatedList));
      
      console.log('MindBookPersistenceService: Deleted MindBook:', mindBookId);
      return true;
    } catch (error) {
      console.error('MindBookPersistenceService: Failed to delete MindBook:', error);
      return false;
    }
  }

  /**
   * Get list of all saved MindBooks
   */
  public getMindBooksList(): MindBookListItem[] {
    try {
      const savedList = localStorage.getItem(MINDBOOKS_LIST_KEY);
      
      if (!savedList) {
        return [];
      }
      
      const list: MindBookListItem[] = JSON.parse(savedList);
      
      // Sort by savedAt (most recent first)
      return list.sort((a, b) => b.savedAt - a.savedAt);
    } catch (error) {
      console.error('MindBookPersistenceService: Failed to get MindBooks list:', error);
      return [];
    }
  }

  /**
   * Get info about the current session
   */
  public getSessionInfo(): { hasAutoSave: boolean; hasSavedMindBooks: boolean; sheetsCount: number; lastSaved?: number } {
    const store = useMindBookStore.getState();
    const autoSave = localStorage.getItem(AUTO_SAVE_KEY);
    const mindBooksList = this.getMindBooksList();

    let lastSaved: number | undefined;
    if (autoSave) {
      try {
        const sessionData: MindBookData = JSON.parse(autoSave);
        lastSaved = sessionData.savedAt;
      } catch (error) {
        console.error('MindBookPersistenceService: Error parsing auto-save data for timestamp:', error);
      }
    }

    return {
      hasAutoSave: !!autoSave,
      hasSavedMindBooks: mindBooksList.length > 0,
      sheetsCount: store.sheets.length,
      lastSaved
    };
  }

  /**
   * Schedule auto-save with debouncing
   */
  public scheduleAutoSave(delay: number = 2000): void {
    if (this.autoSaveTimeout) {
      clearTimeout(this.autoSaveTimeout);
    }
    
    this.autoSaveTimeout = setTimeout(() => {
      this.autoSaveSession();
    }, delay);
  }

  /**
   * Clean up old data and migrate from legacy systems
   */
  public migrateAndCleanup(): void {
    try {
      console.log('MindBookPersistenceService: Starting migration and cleanup...');
      
      // Get all localStorage keys
      const keys = Object.keys(localStorage);
      
      // Find legacy mindmap projects
      const mindmapKeys = keys.filter(key => key.startsWith('mindmap_') && !key.includes('_sheet_'));
      console.log(`MindBookPersistenceService: Found ${mindmapKeys.length} legacy mindmap projects`);
      
      // Migrate each legacy project to a new MindBook
      mindmapKeys.forEach(key => {
        try {
          const legacyData = localStorage.getItem(key);
          if (!legacyData) return;
          
          const legacyProject = JSON.parse(legacyData);
          console.log('Migrating legacy project:', key);
          
          // Create a new MindBook from the legacy data
          const name = legacyProject.name || 'Migrated Project';
          const description = legacyProject.description || 'Migrated from legacy format';
          
          // Save as a new MindBook
          this.saveMindBook(name, description);
          
          // Remove the legacy data after successful migration
          localStorage.removeItem(key);
        } catch (error) {
          console.error('Failed to migrate legacy project:', key, error);
        }
      });
      
      // Keep these keys
      const keysToKeep = [
        MINDBOOKS_LIST_KEY,
        AUTO_SAVE_KEY,
        ACTIVE_MINDBOOK_KEY
      ];
      
      // Clean up old session persistence keys but preserve important ones
      keys.forEach(key => {
        if (!keysToKeep.includes(key) && 
            !key.startsWith(MINDBOOK_STORAGE_PREFIX) && 
            !key.includes('_sheet_') &&
            !key.startsWith('context_settings_') && // PRESERVE context settings
            !key.includes('context_settings_list') && // PRESERVE context settings list
            !key.includes('current_context_settings')) { // PRESERVE current context settings
          localStorage.removeItem(key);
        }
      });
      
      console.log('MindBookPersistenceService: Migration and cleanup completed');
    } catch (error) {
      console.error('MindBookPersistenceService: Migration failed:', error);
    }
  }

  /**
   * Set context settings for the current session
   */
  public setContextSettings(contextSettingsId: string): boolean {
    try {
      const contextStore = useContextStore.getState();
      const success = contextStore.loadContextSettings(contextSettingsId);
      
      if (success) {
        console.log('MindBookPersistenceService: Context settings loaded:', contextSettingsId);
        // Trigger auto-save to associate the context settings with current session
        this.scheduleAutoSave(500); // Quick auto-save
      }
      
      return success;
    } catch (error) {
      console.error('MindBookPersistenceService: Failed to set context settings:', error);
      return false;
    }
  }

  /**
   * Get current context settings ID
   */
  public getCurrentContextSettingsId(): string | null {
    try {
      const contextStore = useContextStore.getState();
      return contextStore.currentContextSettings?.id || null;
    } catch (error) {
      console.error('MindBookPersistenceService: Failed to get current context settings ID:', error);
      return null;
    }
  }

  /**
   * Ensure there is at least one default MindBook
   */
  public ensureDefaultMindBook(): void {
    try {
      const currentList = this.getMindBooksList();
      
      if (currentList.length === 0) {
        console.log('MindBookPersistenceService: Creating default MindBook...');
        
        // Create a default MindBook
        const success = this.saveMindBook(
          'My First MindBook',
          'Welcome to MindBack! This is your first MindBook.'
        );
        
        if (success) {
          console.log('MindBookPersistenceService: Default MindBook created successfully');
        } else {
          console.error('MindBookPersistenceService: Failed to create default MindBook');
        }
      }
    } catch (error) {
      console.error('MindBookPersistenceService: Failed to ensure default MindBook:', error);
    }
  }

  // Private helper methods

  private restoreMindBookData(mindBookData: MindBookData): boolean {
    try {
      const store = useMindBookStore.getState();

      console.log('MindBookPersistenceService: Restoring', mindBookData.sheets.length, 'sheets...');

      // CRITICAL FIX: Clear all existing sheet stores before loading new project
      this.clearAllSheetStores();

      // Clear existing sheets
      store.setSheets([]);

      // Restore sheets
      store.setSheets(mindBookData.sheets);

      // Restore the MindBook name
      store.setName(mindBookData.name);

      // Restore individual sheet states
      console.log('MindBookPersistenceService: Loading sheet states for', mindBookData.sheets.length, 'sheets');
      this.loadAllSheetStates(mindBookData.sheets);
      console.log('MindBookPersistenceService: ✅ Sheet states loaded');
      
      // Restore active sheet
      if (mindBookData.activeSheetId &&
          mindBookData.sheets.find(s => s.id === mindBookData.activeSheetId)) {
        store.setActiveSheet(mindBookData.activeSheetId);
      }

      // CRITICAL: Restore chat messages for memory continuity
      const chatStore = useChatStore.getState();

      // Debug: Log the mindBookData to see what we're working with
      console.log('MindBookPersistenceService: DEBUG - MindBook data:', {
        name: mindBookData.name,
        hasMessages: !!mindBookData.chatMessages,
        messageCount: mindBookData.chatMessages?.length || 0,
        messages: mindBookData.chatMessages
      });

      if (mindBookData.chatMessages && mindBookData.chatMessages.length > 0) {
        console.log('MindBookPersistenceService: Restoring', mindBookData.chatMessages.length, 'chat messages');

        // Clear existing messages first
        chatStore.clearMessages();

        // Add contextual welcome message
        const welcomeMessage = {
          id: 'welcome',
          text: `Welcome back to "${mindBookData.name}"! Your conversation history has been restored.`,
          sender: 'assistant' as const,
          timestamp: new Date()
        };
        chatStore.addMessage(welcomeMessage);

        // Restore all saved messages
        mindBookData.chatMessages.forEach((message, index) => {
          console.log(`MindBookPersistenceService: Restoring message ${index + 1}:`, message);
          chatStore.addMessage(message);
        });

        console.log('MindBookPersistenceService: ✅ Chat messages restored successfully');

        // Debug: Verify messages were added to store
        const currentMessages = chatStore.messages;
        console.log('MindBookPersistenceService: DEBUG - Current chat store messages after restore:', currentMessages.length);
      } else {
        console.log('MindBookPersistenceService: No chat messages to restore - adding welcome message only');
        // Ensure we have a default welcome message
        chatStore.clearMessages();

        // Add contextual welcome message even when no history
        const welcomeMessage = {
          id: 'welcome',
          text: `Welcome back to "${mindBookData.name}"! Your conversation history has been restored.`,
          sender: 'assistant' as const,
          timestamp: new Date()
        };
        chatStore.addMessage(welcomeMessage);
      }
      
      // ALWAYS manage context settings - either load specific ones or clear them
      const contextStore = useContextStore.getState();
      
      if (mindBookData.contextSettingsId) {
        console.log('MindBookPersistenceService: Loading associated context settings:', mindBookData.contextSettingsId);
        
        // First verify the context settings actually exist in localStorage
        const contextStorageKey = `context_settings_${mindBookData.contextSettingsId}`;
        const contextData = localStorage.getItem(contextStorageKey);
        
        if (contextData) {
          const contextLoaded = contextStore.loadContextSettings(mindBookData.contextSettingsId);
          
          if (contextLoaded) {
            console.log('MindBookPersistenceService: ✅ Successfully restored context settings:', mindBookData.contextSettingsId);
          } else {
            console.warn('MindBookPersistenceService: ❌ Failed to load context settings despite data existing:', mindBookData.contextSettingsId);
            // If loading failed, clear context settings
            contextStore.clearCurrentContextSettings();
          }
        } else {
          console.warn('MindBookPersistenceService: ❌ Context settings data not found in localStorage:', mindBookData.contextSettingsId);
          // Clear context settings since the referenced ones don't exist
          contextStore.clearCurrentContextSettings();
        }
      } else {
        console.log('MindBookPersistenceService: No context settings associated with this MindBook - clearing any existing context');
        // CRITICAL FIX: Clear context settings when mindbook has none
        contextStore.clearCurrentContextSettings();
      }
      
      console.log('MindBookPersistenceService: Successfully restored MindBook:', mindBookData.name);
      return true;
    } catch (error) {
      console.error('MindBookPersistenceService: Failed to restore MindBook data:', error);
      return false;
    }
  }

  private saveAllSheetStates(sheets: SheetData[]): void {
    sheets.forEach(sheet => {
      if (sheet.contentType === MindSheetContentType.MINDMAP) {
        // Save mindmap sheet state
        saveSheetMindMapStoreState(sheet.id);
      }
      // Add other sheet types as needed
    });
  }

  private loadAllSheetStates(sheets: SheetData[]): void {
    sheets.forEach(sheet => {
      if (sheet.contentType === MindSheetContentType.MINDMAP) {
        // Load mindmap sheet state
        loadSheetMindMapStoreState(sheet.id);
      }
      // Add other sheet types as needed
    });
  }

  /**
   * Clear all in-memory sheet stores to prevent conflicts when switching projects
   */
  private clearAllSheetStores(): void {
    try {
      // Get all active sheet IDs and remove their stores
      const activeSheetIds = getActiveSheetIds();
      console.log('MindBookPersistenceService: Clearing', activeSheetIds.length, 'sheet stores');

      activeSheetIds.forEach(sheetId => {
        removeSheetMindMapStore(sheetId);
      });

      console.log('MindBookPersistenceService: All sheet stores cleared');
    } catch (error) {
      console.error('MindBookPersistenceService: Error clearing sheet stores:', error);
    }
  }

  private generateMindBookId(name: string): string {
    // Create a URL-safe ID from the name and timestamp
    const cleanName = name.replace(/[^a-zA-Z0-9]/g, '_').toLowerCase();
    const timestamp = Date.now();
    return `${cleanName}_${timestamp}`;
  }

  private updateMindBooksList(mindBookData: MindBookData): void {
    const currentList = this.getMindBooksList();
    
    // Remove existing entry with same ID
    const filteredList = currentList.filter(item => item.id !== mindBookData.id);
    
    // Add new entry
    const newItem: MindBookListItem = {
      id: mindBookData.id,
      name: mindBookData.name,
      savedAt: mindBookData.savedAt,
      sheetsCount: mindBookData.sheets.length,
      description: mindBookData.metadata?.description
    };
    
    const updatedList = [newItem, ...filteredList];
    
    localStorage.setItem(MINDBOOKS_LIST_KEY, JSON.stringify(updatedList));
  }

  private extractTagsFromSheets(sheets: SheetData[]): string[] {
    const tags = new Set<string>();
    
    sheets.forEach(sheet => {
      tags.add(sheet.contentType);
      
      if (sheet.title) {
        // Extract potential keywords from titles
        const words = sheet.title.toLowerCase().split(/\s+/);
        words.forEach(word => {
          if (word.length > 3) {
            tags.add(word);
          }
        });
      }
    });
    
    return Array.from(tags);
  }
}

// Export singleton instance and convenience functions
const mindBookPersistenceService = MindBookPersistenceService.getInstance();

// Export convenience functions
export const autoSaveSession = () => mindBookPersistenceService.autoSaveSession();
export const restoreAutoSavedSession = () => mindBookPersistenceService.restoreAutoSavedSession();
export const saveMindBook = (name: string, description?: string) => mindBookPersistenceService.saveMindBook(name, description);
export const loadMindBook = (mindBookId: string) => mindBookPersistenceService.loadMindBook(mindBookId);
export const deleteMindBook = (mindBookId: string) => mindBookPersistenceService.deleteMindBook(mindBookId);
export const getMindBooksList = () => mindBookPersistenceService.getMindBooksList();
export const getSessionInfo = () => mindBookPersistenceService.getSessionInfo();
export const scheduleAutoSave = (delay?: number) => mindBookPersistenceService.scheduleAutoSave(delay);
export const migrateAndCleanup = () => mindBookPersistenceService.migrateAndCleanup();
export const setContextSettings = (contextSettingsId: string) => mindBookPersistenceService.setContextSettings(contextSettingsId);
export const getCurrentContextSettingsId = () => mindBookPersistenceService.getCurrentContextSettingsId();
export const ensureDefaultMindBook = () => mindBookPersistenceService.ensureDefaultMindBook();
export const clearSession = () => mindBookPersistenceService.clearSession();

// Export types for external use
export type MindBookMetadata = MindBookListItem;
export type { MindBookListItem, MindBookData };

export default mindBookPersistenceService; 
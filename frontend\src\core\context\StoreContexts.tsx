/**
 * Store Context Providers
 * 
 * This module provides React contexts for accessing stores in a hook-safe manner.
 * All store access should go through these contexts to avoid React Hooks violations.
 */

import React, { createContext, useContext, ReactNode } from 'react';
import { useMindBookStore } from '../state/MindBookStore';
import { useChatForkStore } from '../../components/ChatFork/ChatForkStore';
import { pureStoreRegistry, type PureStoreRegistry } from './StoreRegistry';
import type { MindMapStore } from '../state/MindMapStore';

/**
 * Store Registry Context
 * Provides access to the pure store registry
 */
interface StoreRegistryContextType {
  registry: typeof pureStoreRegistry;
  getStore: (sheetId: string) => MindMapStore;
  hasStore: (sheetId: string) => boolean;
  removeStore: (sheetId: string) => void;
  setActiveSheetId: (sheetId: string) => void;
  getActiveSheetId: () => string | null;
}

const StoreRegistryContext = createContext<StoreRegistryContextType | null>(null);

/**
 * MindBook Store Context
 * Provides access to the global MindBook store
 */
const MindBookStoreContext = createContext<ReturnType<typeof useMindBookStore> | null>(null);

/**
 * ChatFork Store Context
 * Provides access to the global ChatFork store
 */
const ChatForkStoreContext = createContext<ReturnType<typeof useChatForkStore> | null>(null);

/**
 * Store Registry Provider
 * Provides the pure store registry to child components
 */
interface StoreRegistryProviderProps {
  children: ReactNode;
}

export const StoreRegistryProvider: React.FC<StoreRegistryProviderProps> = ({ children }) => {
  const contextValue: StoreRegistryContextType = {
    registry: pureStoreRegistry,
    getStore: (sheetId: string) => pureStoreRegistry.getStore(sheetId),
    hasStore: (sheetId: string) => pureStoreRegistry.hasStore(sheetId),
    removeStore: (sheetId: string) => pureStoreRegistry.removeStore(sheetId),
    setActiveSheetId: (sheetId: string) => pureStoreRegistry.setActiveSheetId(sheetId),
    getActiveSheetId: () => pureStoreRegistry.getActiveSheetId(),
  };

  return (
    <StoreRegistryContext.Provider value={contextValue}>
      {children}
    </StoreRegistryContext.Provider>
  );
};

/**
 * MindBook Store Provider
 * Provides the MindBook store to child components
 */
interface MindBookStoreProviderProps {
  children: ReactNode;
}

export const MindBookStoreProvider: React.FC<MindBookStoreProviderProps> = ({ children }) => {
  // This is the ONLY place where we call the Zustand hook
  const mindBookStore = useMindBookStore();

  return (
    <MindBookStoreContext.Provider value={mindBookStore}>
      {children}
    </MindBookStoreContext.Provider>
  );
};

/**
 * ChatFork Store Provider
 * Provides the ChatFork store to child components
 */
interface ChatForkStoreProviderProps {
  children: ReactNode;
}

export const ChatForkStoreProvider: React.FC<ChatForkStoreProviderProps> = ({ children }) => {
  // This is the ONLY place where we call the Zustand hook
  const chatForkStore = useChatForkStore();

  return (
    <ChatForkStoreContext.Provider value={chatForkStore}>
      {children}
    </ChatForkStoreContext.Provider>
  );
};

/**
 * Combined Store Provider
 * Provides all stores to child components
 */
interface StoreProviderProps {
  children: ReactNode;
}

export const StoreProvider: React.FC<StoreProviderProps> = ({ children }) => {
  return (
    <StoreRegistryProvider>
      <MindBookStoreProvider>
        <ChatForkStoreProvider>
          {children}
        </ChatForkStoreProvider>
      </MindBookStoreProvider>
    </StoreRegistryProvider>
  );
};

/**
 * Hook-safe store access hooks
 * These are the ONLY hooks that should be used to access stores
 */

export function useStoreRegistry(): StoreRegistryContextType {
  const context = useContext(StoreRegistryContext);
  if (!context) {
    throw new Error('useStoreRegistry must be used within a StoreRegistryProvider');
  }
  return context;
}

export function useMindBookStoreContext() {
  const context = useContext(MindBookStoreContext);
  if (!context) {
    throw new Error('useMindBookStoreContext must be used within a MindBookStoreProvider');
  }
  return context;
}

export function useChatForkStoreContext() {
  const context = useContext(ChatForkStoreContext);
  if (!context) {
    throw new Error('useChatForkStoreContext must be used within a ChatForkStoreProvider');
  }
  return context;
}

/**
 * Convenience hooks for common operations
 */

export function useMindMapStore(sheetId: string): MindMapStore {
  const { getStore } = useStoreRegistry();
  return getStore(sheetId);
}

export function useActiveSheetStore(): MindMapStore | null {
  const { getActiveSheetId, getStore } = useStoreRegistry();
  const activeSheetId = getActiveSheetId();
  
  if (!activeSheetId) {
    return null;
  }
  
  return getStore(activeSheetId);
}

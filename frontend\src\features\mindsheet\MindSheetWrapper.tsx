/**
 * MindSheetWrapper Component
 *
 * A wrapper component that handles all store access for MindSheet.
 * This component ensures that Zustand store hooks are called at the top level
 * and passes down store state and actions as props to the MindSheet component.
 *
 * This follows the container/presentational component pattern where:
 * - MindSheetWrapper (container) handles store access and data fetching
 * - MindSheet (presentational) focuses on rendering based on props
 */

import React, { useEffect } from 'react';
import MindSheet, { MindSheetProps } from './MindSheet';
import { MindSheetContentType } from '../../core/state/StoreTypes';
import RegistrationManager, { EventType } from '../../core/services/RegistrationManager';
import {
  useStoreRegistry,
  useMindBookStoreContext,
  useChatForkStoreContext
} from '../../core/context/StoreContexts';

interface MindSheetWrapperProps {
  id: string;
  title: string;
  contentType: MindSheetContentType;
  isActive: boolean;
  content: any;
  onActivate?: () => void;
}

const MindSheetWrapper: React.FC<MindSheetWrapperProps> = (props) => {
  // Get stores from contexts - proper hook usage
  const storeRegistry = useStoreRegistry();
  const mindBookStore = useMindBookStoreContext();
  const chatForkStore = useChatForkStoreContext();

  // Get the sheet-specific store using the context registry
  const sheetStore = storeRegistry.getStore(props.id);

  // Log when the wrapper mounts and unmounts
  useEffect(() => {
    console.log('MindSheetWrapper: Mounted for sheet:', props.id, 'contentType:', props.contentType);

    // Ensure the store exists for this sheet
    if (props.contentType === MindSheetContentType.MINDMAP) {
      if (!storeRegistry.hasStore(props.id)) {
        console.log('MindSheetWrapper: Creating new store for sheet:', props.id);
        // Store is automatically created by getStore call above
      } else {
        console.log('MindSheetWrapper: Using existing store for sheet:', props.id);
      }

      // Register the sheet creation event if it's active
      if (props.isActive) {
        RegistrationManager.registerEvent(EventType.SHEET_ACTIVATED, {
          id: props.id,
          type: props.contentType.toLowerCase()
        });
      }
    }

    return () => {
      console.log('MindSheetWrapper: Unmounted for sheet:', props.id);
    };
  }, [props.id, props.contentType, props.isActive, storeRegistry]);

  // Pass down all props and store references to the MindSheet component
  return (
    <MindSheet
      {...props}
      mindMapStore={null} // Legacy prop - not used anymore
      mindBookStore={mindBookStore}
      chatForkStore={chatForkStore}
      sheetStore={sheetStore}
    />
  );
};

export default MindSheetWrapper;

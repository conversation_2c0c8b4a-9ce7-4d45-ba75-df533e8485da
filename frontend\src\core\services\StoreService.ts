/**
 * Store Service
 * 
 * Centralized service for managing sheet-specific MindMap stores
 * and handling store state persistence.
 */

import { useMindBookStore } from '../state/MindBookStore';
import { useMindMapStore } from '../state/MindMapStore';
import { useChatForkStore } from '../../components/ChatFork/ChatForkStore';
import { pureStoreRegistry } from '../context/StoreRegistry';
import { createMindMapStore } from '../state/MindMapStore';

/**
 * StoreService class
 * 
 * Provides methods for accessing all stores in the application.
 * This is a singleton class that can be imported anywhere in the application.
 */
class StoreService {
  private static instance: StoreService;

  private constructor() {
    // Private constructor to prevent direct instantiation
    console.log('StoreService: Initialized');
  }

  /**
   * Get the singleton instance of the StoreService
   */
  public static getInstance(): StoreService {
    if (!StoreService.instance) {
      StoreService.instance = new StoreService();
    }
    return StoreService.instance;
  }

  /**
   * Get the MindBookStore
   */
  public getMindBookStore() {
    return useMindBookStore.getState();
  }

  /**
   * Get the MindMapStore
   */
  public getMindMapStore() {
    return useMindMapStore.getState();
  }

  /**
   * Get the ChatForkStore
   */
  public getChatForkStore() {
    return useChatForkStore.getState();
  }
}

// Export the singleton instance
export const storeService = StoreService.getInstance();

// Export convenience functions for basic stores
export const getMindBookStore = () => storeService.getMindBookStore();
export const getMindMapStore = () => storeService.getMindMapStore();
export const getChatForkStore = () => storeService.getChatForkStore();

/**
 * Get or create a sheet-specific MindMap store using the pure store registry
 * This ensures compatibility with the new context-based store system
 */
export const getSheetMindMapStore = (sheetId: string) => {
  console.log(`[StoreService] Getting MindMap store for sheet: ${sheetId}`);
  return pureStoreRegistry.getStore(sheetId);
};

/**
 * Check if a sheet store exists
 */
export const hasSheetMindMapStore = (sheetId: string): boolean => {
  return pureStoreRegistry.hasStore(sheetId);
};

/**
 * Remove a sheet store (cleanup)
 */
export const removeSheetMindMapStore = (sheetId: string): boolean => {
  console.log(`[StoreService] Removing MindMap store for sheet: ${sheetId}`);
  pureStoreRegistry.removeStore(sheetId);
  return true;
};

/**
 * Get all active sheet IDs
 */
export const getActiveSheetIds = (): string[] => {
  return pureStoreRegistry.getAllStoreIds();
};

/**
 * Get store count for monitoring
 */
export const getStoreCount = (): number => {
  return pureStoreRegistry.getAllStoreIds().length;
};

/**
 * Save sheet store state to persistence layer
 */
export const saveSheetMindMapStoreState = (sheetId: string): boolean => {
  try {
    const store = pureStoreRegistry.getStore(sheetId);
    if (!store) {
      console.warn(`[StoreService] Cannot save state: no store found for sheet ${sheetId}`);
      return false;
    }

    const state = store.getState();
    const stateToSave = {
      sheetId: state.sheetId,
      nodes: state.nodes,
      connections: state.connections,
      position: state.position,
      scale: state.scale,
      currentLayoutStrategy: state.currentLayoutStrategy,
      projectName: state.projectName,
      rootNodeId: state.rootNodeId,
      savedAt: Date.now()
    };

    localStorage.setItem(`mindmap_sheet_${sheetId}`, JSON.stringify(stateToSave));
    console.log(`[StoreService] ✅ Saved state for sheet: ${sheetId}`);
    console.log(`[StoreService] Saved ${Object.keys(stateToSave.nodes || {}).length} nodes and ${(stateToSave.connections || []).length} connections`);
    return true;
  } catch (error) {
    console.error(`[StoreService] Error saving state for sheet ${sheetId}:`, error);
    return false;
  }
};

/**
 * Load sheet store state from persistence layer
 */
export const loadSheetMindMapStoreState = (sheetId: string): boolean => {
  try {
    const savedData = localStorage.getItem(`mindmap_sheet_${sheetId}`);
    if (!savedData) {
      console.log(`[StoreService] No saved state found for sheet: ${sheetId}`);
      return false;
    }

    const parsedData = JSON.parse(savedData);
    const store = getSheetMindMapStore(sheetId); // This will create if not exists
    
    // CRITICAL FIX: Use setState() instead of Object.assign() to trigger Zustand reactivity
    store.setState({
      nodes: parsedData.nodes || {},
      connections: parsedData.connections || [],
      position: parsedData.position || { x: 0, y: 0 },
      scale: parsedData.scale || 1.0,
      currentLayoutStrategy: parsedData.currentLayoutStrategy || 'leftToRight',
      projectName: parsedData.projectName || '',
      rootNodeId: parsedData.rootNodeId || null,
      selectedNodeId: null, // Reset selection when loading
      selectedConnectionId: null // Reset selection when loading
    });

    console.log(`[StoreService] ✅ Successfully loaded state for sheet: ${sheetId}`);
    console.log(`[StoreService] Restored ${Object.keys(parsedData.nodes || {}).length} nodes and ${(parsedData.connections || []).length} connections`);
    return true;
  } catch (error) {
    console.error(`[StoreService] Error loading state for sheet ${sheetId}:`, error);
    return false;
  }
};

/**
 * Clear all saved sheet states (for cleanup/reset)
 */
export const clearAllSavedStates = (): void => {
  try {
    const keys = Object.keys(localStorage);
    const sheetKeys = keys.filter(key => key.startsWith('mindmap_sheet_'));
    
    sheetKeys.forEach(key => {
      localStorage.removeItem(key);
    });
    
    console.log(`[StoreService] Cleared ${sheetKeys.length} saved sheet states`);
  } catch (error) {
    console.error('[StoreService] Error clearing saved states:', error);
  }
};

/**
 * Get store statistics for monitoring
 */
export const getStoreStatistics = () => {
  const allStoreIds = pureStoreRegistry.getAllStoreIds();
  const stats = {
    activeStores: allStoreIds.length,
    sheetIds: allStoreIds,
    savedStates: 0
  };

  try {
    const keys = Object.keys(localStorage);
    stats.savedStates = keys.filter(key => key.startsWith('mindmap_sheet_')).length;
  } catch (error) {
    console.warn('[StoreService] Could not count saved states:', error);
  }

  return stats;
};

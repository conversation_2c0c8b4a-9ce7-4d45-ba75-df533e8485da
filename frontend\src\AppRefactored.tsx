/**
 * AppRefactored.tsx
 *
 * Refactored main application component that integrates the MindBook architecture.
 * This version uses the sheet-based approach with MindBook and MindSheets.
 */

import React, { useState, useRef, useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { MindBook } from './features/mindsheet';
import GovernanceBoxPositioned from './governance/chat/GovernanceBoxPositioned';
import { processGovernanceAction } from './core/adapters/GovernanceMindBookAdapter';
import { ContextPanel, ContextPanelPositioned } from './features/context';
import NodeBox from './features/mindmap/components/NodeBox';
// import MindBookManagerDialog from './components/dialogs/MindBookManagerDialog'; // DEPRECATED
import FooterGovernanceButton from './governance/chat/components/FooterGovernanceButton';
import FooterContextButton from './features/context/components/FooterContextButton';
import MindSheetTabs from './features/mindsheet/MindSheetTabs';
import ProjectDialog from './features/mindmap/components/Dialogs/ProjectDialog';
import { useMindMapStore } from './core/state/MindMapStore';
// Import KeyboardManager to ensure it's initialized
import './core/services/KeyboardManager';
import { initKeyboardHandler } from './utils/keyboardHandler';
// Import the new unified MindBook persistence service
import { 
  restoreAutoSavedSession, 
  scheduleAutoSave, 
  getSessionInfo,
  migrateAndCleanup,
  autoSaveSession
} from './core/services/MindBookPersistenceService';
import { useMindBookStore } from './core/state/MindBookStore';
import { useChatStore } from './governance/chat/state/ChatStore';
import { useContextStore } from './features/context/store/ContextStore';
import './App.css';
import './styles/mui-overrides.css';
import './styles/animations.css';
import { PositioningManagerProvider } from './core/positioning';

// Import Startup Screen components
import StartupScreen from './components/StartupScreen';
import HamburgerMenu from './components/HamburgerMenu';
import ContextManagerDialog from './features/context/components/ContextManagerDialog';
import ErrorBoundary from './components/ErrorBoundary';

// Import the asset utility at the top of the file, where other imports are
import { LogoAssets } from './utils/assetPaths';
import { ChatForkAdapter } from './components/MindMap/core/adapters/ChatForkAdapter';
import { ChatForkView } from './components/ChatFork';
import RegistrationManager, { EventType } from './core/services/RegistrationManager';
import MemoryTestRunner from './components/testing/MemoryTestRunner';
import { StoreProvider } from './core/context/StoreContexts';

const AppRefactored: React.FC = () => {
  // App state management
  const [appState, setAppState] = useState<'startup' | 'working'>('startup');
  const [showHamburgerMenu, setShowHamburgerMenu] = useState(false);
  const [showContextManager, setShowContextManager] = useState(false);

  // State for governance chat
  const [showGovernanceChat, setShowGovernanceChat] = useState(true);
  const [isGovernanceChatCollapsed, setIsGovernanceChatCollapsed] = useState(false);
  const [isGovernanceChatFullyCollapsed, setIsGovernanceChatFullyCollapsed] = useState(false);

  // State for context panel
  const [isContextPanelOpen, setIsContextPanelOpen] = useState(false);

  // State for MindBook Manager - DEPRECATED
  // const [showMindBookManager, setShowMindBookManager] = useState(false);

  // State for session restoration
  const [isRestoringSession, setIsRestoringSession] = useState(false);
  const [userRequestedStartup, setUserRequestedStartup] = useState(false);

  // State for memory test runner
  const [showMemoryTestRunner, setShowMemoryTestRunner] = useState(false);

  // Replace the existing logo state with our utility
  // Before:
  // const [headerLogoSrc, setHeaderLogoSrc] = useState('./Public/Logo/MB_logo.jpg');
  // const [headerLogoHasError, setHeaderLogoHasError] = useState(false);

  // Fix hook order violation: useState must come before any code that uses it
  const [headerLogoRetries, setHeaderLogoRetries] = useState(0);

  // After useState is declared, we can create logoConfig that uses setHeaderLogoRetries
  const logoConfig = LogoAssets.getPrimaryLogo((e) => {
    // Custom error handler that adds a cache buster on error
    if (headerLogoRetries < 2) {
      setHeaderLogoRetries(headerLogoRetries + 1);
      console.log(`AppRefactored: Logo load failed, retry ${headerLogoRetries + 1} of 2`);
    }
  });

  // MindBook store
  const mindBookStore = useMindBookStore();

  // Project dialog state from MindMapStore
  const { showProjectDialog, setShowProjectDialog } = useMindMapStore();

  // Initialize global keyboard handler
  useEffect(() => {
    console.log('AppRefactored: Initializing global keyboard handler');
    const cleanup = initKeyboardHandler();
    return cleanup;
  }, []);

  // Initialize the ChatFork global key handler
  useEffect(() => {
    ChatForkAdapter.initializeGlobalKeyHandler();
    console.log('Initialized ChatFork global key handler');
  }, []);

  // Set up auto-save subscription to MindBook store
  useEffect(() => {
    console.log('AppRefactored: Setting up MindBook store subscription for auto-save');

    // FIXED: Get the store reference outside of useEffect to avoid hook violations
    const store = useMindBookStore.getState();
    const unsubscribe = useMindBookStore.subscribe((state) => {
      // Only auto-save if we're in working state and have sheets
      if (appState === 'working' && state.sheets.length > 0) {
        console.log('AppRefactored: MindBook state changed, auto-saving session...');
        autoSaveSession();
      }
    });

    return unsubscribe;
  }, [appState]);

  // Initialize session restoration on app start
  useEffect(() => {
    const initializeSession = async () => {
      setIsRestoringSession(true);
      
      try {
        // Set up error handling
        window.addEventListener('error', (e) => {
          console.error('Global error:', e.error);
          RegistrationManager.registerEvent(EventType.SYSTEM_ERROR, {
            message: e.error?.message || e.message,
            filename: e.filename,
            lineno: e.lineno,
            colno: e.colno
          });
        });

        window.addEventListener('unhandledrejection', (e) => {
          console.error('Unhandled promise rejection:', e.reason);
          RegistrationManager.registerEvent(EventType.SYSTEM_ERROR, {
            message: 'Unhandled Promise Rejection: ' + e.reason,
            type: 'promise_rejection'
          });
        });

        // DISABLED: Automatic session restoration - always show start page first
        console.log('AppRefactored: Automatic session restoration disabled - showing startup screen');

        const sessionInfo = getSessionInfo();
        console.log('AppRefactored: Session info available:', sessionInfo);

        // Always show startup screen first, let user choose to restore session
        console.log('AppRefactored: Showing startup screen - user can choose to restore session');
        setAppState('startup');

      } catch (error) {
        console.error('AppRefactored: Error during session initialization', error);
        RegistrationManager.registerEvent(EventType.SYSTEM_ERROR, {
          message: 'Session initialization failed: ' + (error instanceof Error ? error.message : String(error)),
          context: 'app_startup'
        });
        setAppState('startup');
      } finally {
        setIsRestoringSession(false);
      }
    };

    initializeSession();
  }, []);

  // Auto-save session when mindbook state changes
  useEffect(() => {
    console.log('AppRefactored: Setting up MindBookStore subscription for auto-save');

    // FIXED: Get the store reference outside of useEffect to avoid hook violations
    const store = useMindBookStore.getState();
    const unsubscribe = useMindBookStore.subscribe(
      (state) => {
        console.log('AppRefactored: MindBookStore state changed. Sheets count:', state.sheets.length);
        if (state.sheets.length > 0) {
          console.log('AppRefactored: Scheduling auto-save...');
          scheduleAutoSave(2000); // 2 second delay
        }
      }
    );

    return unsubscribe;
  }, []);

  // Save session before page unload
  useEffect(() => {
    const handleBeforeUnload = (event: BeforeUnloadEvent) => {
      try {
        // Immediate auto-save before page unload
        autoSaveSession();
        console.log('AppRefactored: Auto-saved before page unload');
      } catch (error) {
        console.error('AppRefactored: Error saving before unload:', error);
      }
    };

    window.addEventListener('beforeunload', handleBeforeUnload);
    return () => window.removeEventListener('beforeunload', handleBeforeUnload);
  }, []);

  // Watch for changes in MindBook store to determine app state
  useEffect(() => {
    // TEMPORARILY DISABLED: Auto-switching to working mode to allow seeing start page
    // Only automatically switch to working if user hasn't explicitly requested startup
    // AND we're not currently restoring a session AND app is in startup state
    if (false && // DISABLED: Set to false to prevent auto-switching
        mindBookStore.sheets.length > 0 && 
        appState === 'startup' && 
        !userRequestedStartup && 
        !isRestoringSession) {
      console.log('AppRefactored: Sheets found, automatically switching to working state');
      setAppState('working');
    }
  }, [mindBookStore.sheets.length, appState, userRequestedStartup, isRestoringSession]);

  // Check for auto-saved session on startup and manage app state
  useEffect(() => {
    const checkForAutoSavedSession = async () => {
      try {
        // TEMPORARILY DISABLED: Auto session checking to show start page with saved sessions
        // Only check and auto-switch if user hasn't explicitly requested startup
        if (false && !userRequestedStartup && !isRestoringSession) { // DISABLED
          const sessionInfo = getSessionInfo();
          if (sessionInfo.hasAutoSave || mindBookStore.sheets.length > 0) {
            console.log('AppRefactored: Auto-saved session or existing sheets found, moving to working state');
            setAppState('working');
          }
        }
      } catch (error) {
        console.error('AppRefactored: Failed to check for auto-saved session:', error);
      }
    };

    checkForAutoSavedSession();
  }, [userRequestedStartup, isRestoringSession]);

  // Handle governance action
  const handleGovernanceAction = (action: any) => {
    console.log('Governance action:', action);

    // Process the action using the adapter
    const success = processGovernanceAction(action);

    console.log('Action processed successfully:', success);

    return success;
  };

  // Handle governance close - mark as fully collapsed
  const handleGovernanceClose = () => {
    setShowGovernanceChat(false);
    setIsGovernanceChatFullyCollapsed(true);
  };

  // Handle governance open from footer button
  const handleGovernanceOpen = () => {
    setIsGovernanceChatFullyCollapsed(false);
    setShowGovernanceChat(true);
    setIsGovernanceChatCollapsed(false);
  };

  // Project dialog handlers
  const handleShowProjectDialog = () => {
    setShowProjectDialog(true);
  };

  const handleCloseProjectDialog = () => {
    setShowProjectDialog(false);
  };

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Global shortcuts (work in both modes)
      if ((event.ctrlKey || event.metaKey) && event.shiftKey && event.key === 'M') {
        event.preventDefault();
        setShowMemoryTestRunner(true);
        console.log('🧪 Opening Memory Test Runner (Ctrl+Shift+M)');
        return;
      }

      // In startup mode, provide hidden shortcuts
      if (appState === 'startup') {
        if (event.key === 'Escape') {
          setShowHamburgerMenu(true);
        } else if ((event.ctrlKey || event.metaKey) && event.key === 'k') {
          event.preventDefault();
          handleNewMindBook();
        }
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [appState]);

  // Startup screen handlers
  const handleNavigateToStartup = () => {
    // Set URL parameter to indicate user-requested startup
    const url = new URL(window.location.href);
    url.searchParams.set('userRequested', 'true');
    window.history.replaceState({}, '', url);
    
    setUserRequestedStartup(true);
    setAppState('startup');
    setShowHamburgerMenu(false);
  };

  const handleNavigateToMindBook = () => {
    // DO NOT clear context settings - they should persist with the loaded mindbook
    // Context settings will be automatically restored when a mindbook is loaded
    console.log('AppRefactored: Navigating to MindBook - attempting to restore auto-saved session');

    try {
      // First, try to restore any auto-saved session
      const sessionInfo = getSessionInfo();
      if (sessionInfo.hasAutoSave) {
        console.log('AppRefactored: Found auto-saved session, restoring...');
        const restored = restoreAutoSavedSession();
        if (restored) {
          console.log('AppRefactored: Auto-saved session successfully restored');
        } else {
          console.warn('AppRefactored: Failed to restore auto-saved session');
        }
      } else {
        console.log('AppRefactored: No auto-saved session found');
      }
    } catch (error) {
      console.error('AppRefactored: Error restoring session:', error);
    }

    // Clear URL parameter when going to working state
    const url = new URL(window.location.href);
    url.searchParams.delete('userRequested');
    window.history.replaceState({}, '', url);

    // Close any open dialogs when navigating to working state
    setShowProjectDialog(false);
    setShowHamburgerMenu(false); // Close deprecated hamburger menu

    setUserRequestedStartup(false);
    setAppState('working');
  };

  const handleNewMindBook = () => {
    try {
      console.log('AppRefactored: Creating new MindBook - clearing session first');
      
      // Clear any existing session data
      mindBookStore.setSheets([]);
      mindBookStore.setName(null);
      
      // Clear chat messages to start fresh with welcome message
      const chatStore = useChatStore.getState();
      chatStore.clearMessages();
      
      // Clear context settings for new session
      const contextStore = useContextStore.getState();
      contextStore.clearCurrentContextSettings();
      
      // Clear only auto-save and session data, preserve saved MindBooks
      const keysToRemove = Object.keys(localStorage).filter(k => 
        k.includes('mindbook_autosave') || 
        k.includes('active_mindbook') ||
        (k.startsWith('mindmap_sheet_') && !k.includes('_saved_'))
      );
      keysToRemove.forEach(key => localStorage.removeItem(key));
      
      // Register new MindBook creation
      RegistrationManager.registerEvent(EventType.MINDBOOK_CREATED, {
        name: 'New MindBook',
        timestamp: new Date().toISOString(),
        method: 'user_initiated'
      });
      
      // Clear URL parameter when creating new MindBook
      const url = new URL(window.location.href);
      url.searchParams.delete('userRequested');
      window.history.replaceState({}, '', url);
      
      console.log('AppRefactored: Session, chat, and context cleared, starting fresh MindBook');
      
      // Small delay to ensure all clearing operations complete
      setTimeout(() => {
        // Close deprecated menu and go to working state
        setShowHamburgerMenu(false);
        setUserRequestedStartup(false);
        setAppState('working');
      }, 100);
    } catch (error) {
      console.error('AppRefactored: Error creating new MindBook:', error);
      RegistrationManager.registerEvent(EventType.SYSTEM_ERROR, {
        message: 'Failed to create new MindBook: ' + (error instanceof Error ? error.message : String(error)),
        context: 'new_mindbook_creation'
      });
    }
  };

  const handleBrowseAll = () => {
    setShowProjectDialog(true);
  };

  const handleLogoClick = () => {
    // Clear any auto-save state first
    const keysToRemove = Object.keys(localStorage).filter(k => k.includes('mindbook_autosave'));
    keysToRemove.forEach(key => localStorage.removeItem(key));
    
    // Clear URL parameters and set userRequested flag
    const url = new URL(window.location.href);
    url.searchParams.delete('userRequested');
    url.searchParams.set('userRequested', 'true');
    window.history.replaceState({}, '', url);
    
    setUserRequestedStartup(true);
    setAppState('startup');
  };

  // Show loading state during session restoration
  if (isRestoringSession) {
    return (
      <div className="app-container" style={{ 
        display: 'flex', 
        alignItems: 'center', 
        justifyContent: 'center', 
        height: '100vh',
        background: '#000000',
        color: 'white'
      }}>
        <div style={{ textAlign: 'center' }}>
          <div style={{ fontSize: '24px', marginBottom: '16px' }}>🔄 Restoring your MindBook...</div>
          <div style={{ fontSize: '14px', opacity: 0.8 }}>
            Loading your saved mindsheets and chats
          </div>
        </div>
      </div>
    );
  }

  // Render startup screen when appState is 'startup'
  if (appState === 'startup') {
    return (
      <StoreProvider>
        <PositioningManagerProvider>
          <Router>
            <div className="app-container" style={{ background: '#000000', height: '100vh', overflow: 'hidden' }}>
            {/* Full screen startup - no header, no footer */}
            <ErrorBoundary>
              <StartupScreen
                onNavigateToMindBook={handleNavigateToMindBook}
                onNewMindBook={handleNewMindBook}
                onBrowseAll={handleBrowseAll}
              />
            </ErrorBoundary>

            {/* Footer only with mindback.ai */}
            <footer style={{
              position: 'fixed',
              bottom: 0,
              left: 0,
              right: 0,
              height: '40px',
              background: 'transparent',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              color: '#666666',
              fontSize: '14px',
              fontWeight: '400',
              zIndex: 1000
            }}>
              mindback.ai
            </footer>

            {/* Project Management Dialog - Visible */}
            {showProjectDialog && (
              <ProjectDialog isOpen={showProjectDialog} onClose={handleCloseProjectDialog} />
            )}

            {/* Hidden dialogs - still need these for functionality but invisible */}
            <div style={{ display: 'none' }}>
              {/* Hamburger Menu - hidden but functional for keyboard shortcuts */}
              <ErrorBoundary>
                <HamburgerMenu
                  isOpen={showHamburgerMenu}
                  onClose={() => setShowHamburgerMenu(false)}
                  onOpenContextManager={() => setShowContextManager(true)}
                  onOpenProjectManager={() => setShowProjectDialog(true)}
                  onNavigateToStartup={handleNavigateToStartup}
                />
              </ErrorBoundary>

              {/* Context Manager Dialog */}
              <ContextManagerDialog
                open={showContextManager}
                onClose={() => setShowContextManager(false)}
              />
            </div>

            {/* Memory Test Runner - Available in startup mode too */}
            {showMemoryTestRunner && (
              <MemoryTestRunner
                onClose={() => setShowMemoryTestRunner(false)}
              />
            )}
          </div>
        </Router>
      </PositioningManagerProvider>
      </StoreProvider>
    );
  }

  // Render working state (main application interface)
  return (
    <StoreProvider>
      <PositioningManagerProvider>
        <Router>
        <div className="app-container">
          {/* Header - restored to original design */}
      <header className="app-header">
            <div className="logo-container">
              <img
                src={logoConfig.src}
                alt="MindBack Logo"
                className="app-header-logo"
                onClick={handleLogoClick}
                style={{ cursor: 'pointer' }}
                title="Return to Home"
                onError={logoConfig.onError}
                onLoad={() => {
                  console.log('AppRefactored: Header logo loaded successfully');
                  setHeaderLogoRetries(0);
                }}
              />
              <span className="logo-slogan"> mindback.ai - intelligence moderation</span>
            </div>
            <div className="header-controls">
              {/* Hamburger Menu button - opens project management */}
              <button
                className="menu-button"
                onClick={handleShowProjectDialog}
                title="Project Management"
              >
                ☰
              </button>
              {/* Help button */}
              <button
                className="help-button" 
                title="Help"
              >
                ?
              </button>
            </div>
          </header>

          {/* MindBook Manager Dialog - DEPRECATED */}
          {/* <MindBookManagerDialog
            isOpen={showMindBookManager}
            onClose={() => setShowMindBookManager(false)}
          /> */}

          {/* Project Management Dialog */}
          {showProjectDialog && (
            <ProjectDialog isOpen={showProjectDialog} onClose={handleCloseProjectDialog} />
          )}

          {/* Main content area */}
          <div className="app-content">
            <Routes>
              <Route path="*" element={
        <MindBook
          governanceComponent={
                    <GovernanceBoxPositioned
                      isOpen={showGovernanceChat && !isGovernanceChatFullyCollapsed}
                isCollapsed={isGovernanceChatCollapsed}
                      onClose={() => setIsGovernanceChatFullyCollapsed(true)}
                onCollapse={() => setIsGovernanceChatCollapsed(!isGovernanceChatCollapsed)}
                onAction={handleGovernanceAction}
                      isContextPanelOpen={isContextPanelOpen}
                    />
                  }
                />
              } />
            </Routes>
          </div>

          {/* MindSheet Tabs - positioned via CSS */}
          <MindSheetTabs />

          {/* Footer - black with centered mindback.ai and functional context button */}
      <footer className="app-footer">
            <div className="session-info" style={{ display: 'flex', alignItems: 'center', maxWidth: '300px' }}>
              <FooterContextButton
                isOpen={isContextPanelOpen}
                onClick={() => setIsContextPanelOpen(!isContextPanelOpen)}
              />
              {mindBookStore.sheets.length > 0 && (
                <span style={{ 
                  color: '#ffffff', 
                  marginLeft: '8px',
                  whiteSpace: 'nowrap',
                  overflow: 'hidden',
                  textOverflow: 'ellipsis'
                }}>
                  {mindBookStore.name || 'Unsaved MindBook'}
                </span>
              )}
            </div>
            <div className="footer-brand">mindback.ai</div>
            {isGovernanceChatFullyCollapsed && (
              <div className="footer-right-controls">
                <FooterGovernanceButton
                  onClick={handleGovernanceOpen}
                />
              </div>
            )}
      </footer>

          {/* Context Panel - Using Positioned Version */}
          <ContextPanelPositioned
            isOpen={isContextPanelOpen}
            onClose={() => setIsContextPanelOpen(false)}
          />

          {/* ChatForkView - Add it here to make it visible */}
          <ChatForkView />

          {/* Hamburger Menu */}
          <ErrorBoundary>
            <HamburgerMenu
              isOpen={showHamburgerMenu}
              onClose={() => setShowHamburgerMenu(false)}
              onOpenContextManager={() => setShowContextManager(true)}
              onOpenProjectManager={() => setShowProjectDialog(true)}
              onNavigateToStartup={handleNavigateToStartup}
            />
          </ErrorBoundary>

      {/* NodeBox Component - Global instance for all MindSheets */}
      <NodeBox />

          {/* Context Manager Dialog */}
          <ContextManagerDialog
            open={showContextManager}
            onClose={() => setShowContextManager(false)}
          />

          {/* Memory Test Runner - Hidden feature (Ctrl+Shift+T) */}
          {showMemoryTestRunner && (
            <MemoryTestRunner
              onClose={() => setShowMemoryTestRunner(false)}
            />
          )}
        </div>
      </Router>
    </PositioningManagerProvider>
    </StoreProvider>
  );
};

export default AppRefactored;
